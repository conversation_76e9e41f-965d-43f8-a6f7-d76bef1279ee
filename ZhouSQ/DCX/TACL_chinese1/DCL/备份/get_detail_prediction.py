#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多知识点题目预测测试脚本
测试模型对多个标签题目的预测效果
"""

import sys
import os
os.environ["CUDA_VISIBLE_DEVICES"] = "1"  # 使用第二块显卡
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import math

import pandas as pd
from predict import HierarchicalTextClassifier
from collections import defaultdict
import json

from datetime import datetime

def load_processed_data(file_path):
    """加载处理后的数据"""
    print(f"📁 加载数据文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    try:
        df = pd.read_json(file_path)
        print(f"✅ 成功加载 {len(df)} 条数据")
        return df
    except Exception as e:
        print(f"❌ 加载文件失败: {e}")
        return None

def test_multi_knowledge_prediction_batch(classifier, df, sample_size=100, batch_size=32):
    """批量测试多知识点题目的预测效果（优化版本）"""
    print(f"\n🔍 批量测试多知识点题目预测效果（采样 {sample_size} 条）...")

    # 按题目分组，找出多知识点题目
    question_groups = df.groupby('doc_token')
    # multi_knowledge_questions = question_groups.filter(lambda x: len(x) > 1)

    # 所有题目
    multi_knowledge_questions = question_groups.filter(lambda x: len(x) > 0)

    if len(multi_knowledge_questions) == 0:
        print("❌ 没有找到多知识点题目")
        return []

    # 获取多知识点题目列表
    unique_multi_questions = multi_knowledge_questions.groupby('doc_token').first()

    print(f"📊 多知识点题目统计:")
    print(f"   - 多知识点题目数量: {len(unique_multi_questions)}")
    print(f"   - 总知识点关联数: {len(multi_knowledge_questions)}")

    # 采样测试
    sample_questions = unique_multi_questions.head(sample_size)
    question_texts = list(sample_questions.index)

    print(f"🚀 开始批量预测...")

    # 批量预测所有题目
    try:
        batch_predictions = classifier.predict_batch(question_texts, topk=3, batch_size=batch_size)
        print(f"✅ 批量预测完成！")
    except Exception as e:
        print(f"❌ 批量预测失败: {e}")
        return []

    # 分析预测结果
    print(f"🔍 分析预测结果...")
    results = []

    for i, (question_text, prediction_result) in enumerate(zip(question_texts, batch_predictions)):
        # 获取该题目的所有真实知识点
        true_knowledge_points = question_groups.get_group(question_text)
        true_knowledge_names = true_knowledge_points['doc_label'].tolist()

        # 分析预测准确性
        predicted_paths = [pred['full_path'] for pred in prediction_result['predictions']]

        # 匹配分析
        matches = []
        true_lable_paths = []
        for true_name in true_knowledge_names:
            if isinstance(true_name, float) and math.isnan(true_name):
                continue

            # 将数组格式的true_name转换为字符串格式
            if isinstance(true_name, list):
                true_name_str = ' -> '.join(true_name)
                true_lable_paths.append(true_name_str)
            else:
                true_name_str = str(true_name)

            for j, pred_path in enumerate(predicted_paths): 
                if true_name_str == pred_path:  # 使用精确匹配
                    matches.append({
                        'true_knowledge': true_name_str,
                        'predicted_rank': j + 1,
                        'predicted_path': pred_path,
                        'confidence': prediction_result['predictions'][j]['confidence']
                    })
                    break

        results.append({
            'question': question_text,
            'true_knowledge_count': len(true_knowledge_names),
            'true_lable_paths': true_lable_paths,
            'predictions': prediction_result['predictions'],
            'matches': matches,
            'match_rate': len(matches) / len(true_knowledge_names) if true_knowledge_names else 0
        })

        # 显示进度
        if (i + 1) % 50 == 0:
            print(f"   已分析 {i + 1}/{len(question_texts)} 条题目...")

    print(f"✅ 结果分析完成！")
    return results

def test_multi_knowledge_prediction(classifier, df, sample_size=20):
    """单条测试多知识点题目的预测效果（保留原有方法用于详细分析）"""
    print(f"\n🔍 详细测试多知识点题目预测效果（采样 {sample_size} 条）...")

    # 按题目分组，找出多知识点题目
    question_groups = df.groupby('html清洗题干')
    multi_knowledge_questions = question_groups.filter(lambda x: len(x) > 1)

    if len(multi_knowledge_questions) == 0:
        print("❌ 没有找到多知识点题目")
        return []

    # 获取多知识点题目列表
    unique_multi_questions = multi_knowledge_questions.groupby('html清洗题干').first()

    print(f"📊 多知识点题目统计:")
    print(f"   - 多知识点题目数量: {len(unique_multi_questions)}")
    print(f"   - 总知识点关联数: {len(multi_knowledge_questions)}")

    # 采样测试
    sample_questions = unique_multi_questions.head(sample_size)

    results = []

    for i, (question_text, row) in enumerate(sample_questions.iterrows(), 1):
        print(f"\n{'='*60}")
        print(f"测试题目 {i}/{len(sample_questions)}")
        print(f"题目: {question_text[:100]}...")

        # 获取该题目的所有真实知识点
        true_knowledge_points = question_groups.get_group(question_text)
        true_knowledge_ids = true_knowledge_points['知识点ID'].tolist()
        true_knowledge_names = true_knowledge_points['知识点名称'].tolist()

        print(f"真实知识点数量: {len(true_knowledge_ids)}")
        for j, (kid, kname) in enumerate(zip(true_knowledge_ids, true_knowledge_names)):
            print(f"   {j+1}. ID={kid}, 名称='{kname}'")

        try:
            # 进行预测
            prediction_result = classifier.predict(question_text, topk=5)

            print(f"\n预测结果:")
            for j, pred in enumerate(prediction_result['predictions']):
                print(f"   Top-{j+1}: {pred['full_path']}")
                print(f"          置信度: {pred['confidence']:.4f}")

            # 检查是否有重复路径
            predicted_paths = [pred['full_path'] for pred in prediction_result['predictions']]
            unique_paths = set(predicted_paths)
            if len(unique_paths) != len(predicted_paths):
                print(f"   ⚠️ 发现重复路径！去重前: {len(predicted_paths)}, 去重后: {len(unique_paths)}")
            else:
                print(f"   ✅ 无重复路径")

            # 匹配分析
            matches = []
            for true_name in true_knowledge_names:
                # 将数组格式的true_name转换为字符串格式
                if isinstance(true_name, list):
                    true_name_str = ' -> '.join(true_name)
                else:
                    true_name_str = str(true_name)

                for j, pred_path in enumerate(predicted_paths):
                    if true_name_str == pred_path:  # 使用精确匹配
                        matches.append({
                            'true_knowledge': true_name_str,
                            'predicted_rank': j + 1,
                            'predicted_path': pred_path,
                            'confidence': prediction_result['predictions'][j]['confidence']
                        })
                        break

            print(f"\n匹配分析:")
            if matches:
                print(f"   ✅ 匹配到 {len(matches)}/{len(true_knowledge_names)} 个知识点")
                for match in matches:
                    print(f"      '{match['true_knowledge']}' -> Top-{match['predicted_rank']} (置信度: {match['confidence']:.4f})")
            else:
                print(f"   ❌ 没有匹配到任何知识点")

            results.append({
                'question': question_text,
                'true_knowledge_count': len(true_knowledge_ids),
                'true_knowledge_names': true_knowledge_names,
                'predicted_paths': predicted_paths,
                'matches': matches,
                'match_rate': len(matches) / len(true_knowledge_names) if true_knowledge_names else 0,
                'has_duplicates': len(unique_paths) != len(predicted_paths)
            })

        except Exception as e:
            print(f"❌ 预测失败: {e}")
            results.append({
                'question': question_text,
                'true_knowledge_count': len(true_knowledge_ids),
                'true_knowledge_names': true_knowledge_names,
                'error': str(e),
                'match_rate': 0,
                'has_duplicates': False
            })

    return results

def analyze_prediction_results(results):
    """分析预测结果（适配 TopK 匹配）"""
    print(f"\n📊 预测结果分析:")
    print("="*60)

    if not results:
        print("❌ 没有预测结果可分析")
        return

    # 过滤掉错误的结果
    valid_results = [r for r in results if 'error' not in r]
    error_count = len(results) - len(valid_results)

    print(f"📈 总体统计:")
    print(f"   - 测试题目数量: {len(results)}")
    print(f"   - 成功预测数量: {len(valid_results)}")
    print(f"   - 预测失败数量: {error_count}")

    if not valid_results:
        return

    # 匹配率统计
    match_rates = [r['match_rate'] for r in valid_results]
    avg_match_rate = sum(match_rates) / len(match_rates)

    print(f"\n🎯 匹配效果:")
    print(f"   - 平均匹配率: {avg_match_rate:.2%}")
    print(f"   - 完全匹配题目: {sum(1 for r in match_rates if r == 1.0)}")
    print(f"   - 部分匹配题目: {sum(1 for r in match_rates if 0 < r < 1.0)}")
    print(f"   - 无匹配题目: {sum(1 for r in match_rates if r == 0)}")

    # 知识点数量分析
    knowledge_counts = [r['true_knowledge_count'] for r in valid_results]
    print(f"\n📚 知识点数量分析:")
    print(f"   - 平均知识点数量: {sum(knowledge_counts) / len(knowledge_counts):.2f}")
    print(f"   - 最多知识点数量: {max(knowledge_counts)}")
    print(f"   - 最少知识点数量: {min(knowledge_counts)}")

    # 按知识点数量分组的匹配率统计
    print(f"\n📊 按知识点数量的匹配率（含细粒度TopK命中分析）:")
    group_stats = defaultdict(list)

    for result in valid_results:
        count = result['true_knowledge_count']
        hit_count = len(result.get('matches', []))
        group_stats[count].append(hit_count)

    for k_count in sorted(group_stats.keys()):
        group = group_stats[k_count]
        total = len(group)
        print(f"\n🧩 题目含 {k_count} 个知识点（共 {total} 题）：")
        
        for hit in range(1, k_count + 1):
            hit_count = sum(1 for g in group if g == hit)
            rate = hit_count / total
            print(f"   - 命中 {hit} 个知识点: {hit_count}题，占比 {rate:.2%}")

        # 也可以统计 0 命中的题目（无匹配）
        zero_hit_count = sum(1 for g in group if g == 0)
        if zero_hit_count > 0:
            print(f"   - 未命中任何知识点: {zero_hit_count}题，占比 {zero_hit_count / total:.2%}")


import json

def load_prediction_results(file_path):
    """
    加载预测结果 JSON 文件。
    
    参数:
        file_path (str): JSON 文件路径，文件应包含一个列表，每个元素是一个预测样本。
    
    返回:
        list[dict]: 所有预测结果（每个元素是一个 dict）
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            raise ValueError("JSON 文件内容应为列表形式，每个元素是一条预测记录。")
        
        print(f"✅ 成功加载 {len(data)} 条预测结果")
        return data
    
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return []
    except json.JSONDecodeError:
        print(f"❌ JSON 格式错误，请检查文件: {file_path}")
        return []
    except Exception as e:
        print(f"❌ 发生异常: {e}")
        return []


def main():
    """主函数"""
    print("🎯 多知识点题目预测测试工具")
    print("="*80)
    
    # 1. 加载数据
    data_file ="/home/<USER>/ZhouSQ/DCX/TACL_chinese1/DCL/dataset/WebOfScience/wos_test.json"
    # if not data_file:
    #     print("❌ 请提供有效的文件路径")
    #     return
    
    df = load_processed_data(data_file)
    if df is None:
        return
    
    # 2. 初始化分类器
    print(f"\n🚀 初始化分类器...")
    
    model_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/ckpts/2025-07-18_11-04-04-lr-3e-05-lm_training-1-lm_alpha-0.7-batch_size-8-macro.ckpt"
    embedding_path = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/_30shot_none_171_embed_doc_0.pkl"

    try:
        classifier = HierarchicalTextClassifier(model_path, embedding_path)
        print("✅ 分类器初始化成功")
    except Exception as e:
        print(f"❌ 分类器初始化失败: {e}")
        return
    
    # 3. 测试预测效果
    # sample_size = int(input("请输入测试样本数量（默认10）: ").strip() or "10")
    sample_size = len(df)
    results = test_multi_knowledge_prediction_batch(classifier, df, sample_size, 32)
    
    # 4. 分析结果
    # 定义函数来生成唯一的文件名
    def get_unique_filename(base_filename):
        counter = 1
        new_filename = base_filename
        while os.path.exists(new_filename):  # 如果文件已存在，则生成新的文件名
            base, ext = os.path.splitext(base_filename)
            new_filename = f"{base}-{counter}{ext}"
            counter += 1
        return new_filename

    # 如果有结果，则进行分析并保存
    if results:
        analyze_prediction_results(results)

        # 原始文件名
        base_filename = "/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/multi_knowledge_prediction_results_old_test.json"

        # 获取唯一文件名
        output_file = get_unique_filename(base_filename)

        # 保存结果到新文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)

        print(f"✅ 结果已保存到: {output_file}")

    print(f"\n🏁 测试完成！")


# 打开日志文件，使用 'a' 模式追加日志
log_file = open('/home/<USER>/ZhouSQ/DCX/TACL_chinese1/result/get_detail_prediction.log', 'a', encoding='utf-8')

# 写入当前日期时间
log_file.write(f"--- Log Start: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} ---\n")

# 设置标准输出和错误输出到日志文件
sys.stdout = log_file
sys.stderr = log_file

if __name__ == "__main__":
    main()
