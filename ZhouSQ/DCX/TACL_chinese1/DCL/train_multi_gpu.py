#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多GPU训练脚本
使用device_map="auto"自动分配GPU，不设置CUDA_VISIBLE_DEVICES
"""

import os
import sys
import subprocess

def run_training_multi_gpu():
    """运行多GPU训练"""
    
    # 基本参数
    base_args = [
        "python", "models/train.py",
        "--use_multi_gpu", "True",  # 启用多GPU模式
        "--use_fp16", "True",       # 启用FP16以节省显存
        "--dataset", "wos",
        "--model", "hierVerb",
        "--model_name_or_path", "../chinese-macbert-base",
        "--shot", "16",
        "--seed", "171",
        "--batch_size", "8",        # 多GPU可以使用更大的batch size
        "--lr", "5e-5",
        "--lr2", "1e-4",
        "--max_epochs", "20",
        "--early_stop", "10",
        "--max_seq_lens", "512",
        "--dropout", "0.1",
        "--shuffle", "1",
        "--multi_mask", "1",
        "--freeze_plm", "0",
        "--contrastive_logits", "1",
        "--constraint_loss", "0",
        "--cs_mode", "0",
        "--eval_mode", "0",
        "--use_hier_mean", "1",
        "--multi_label", "0",
        "--multi_verb", "1",
        "--use_scheduler1", "1",
        "--use_scheduler2", "1",
        "--constraint_alpha", "-1",
        "--imbalanced_weight", "True",
        "--imbalanced_weight_reverse", "True",
        "--lm_training", "1",
        "--max_grad_norm", "1.0",
        "--use_new_ct", "1",
        "--contrastive_loss", "0",
        "--contrastive_alpha", "0.99",
        "--contrastive_level", "1",
        "--use_dropout_sim", "0",
        "--use_withoutWrappedLM", "False",
        "--mean_verbalizer", "True",
        "--lm_alpha", "0.999",
        "--label_description", "0",
        "--verbalizer", "soft",
        "--not_manual", "False",
        "--gradient_accumulation_steps", "1",
        "--eval_full", "1"
    ]
    
    print("🚀 开始多GPU训练...")
    print("📊 训练参数:")
    for i in range(0, len(base_args), 2):
        if i + 1 < len(base_args) and base_args[i].startswith('--'):
            print(f"  {base_args[i]}: {base_args[i+1]}")
    
    print("\n🔧 GPU信息:")
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=index,name,memory.total', '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            gpu_info = result.stdout.strip().split('\n')
            for info in gpu_info:
                parts = info.split(', ')
                if len(parts) >= 3:
                    print(f"  GPU {parts[0]}: {parts[1]} ({parts[2]}MB)")
        else:
            print("  无法获取GPU信息")
    except:
        print("  无法获取GPU信息")
    
    print(f"\n🎯 多GPU模式: device_map='auto'")
    print("📝 注意: 不设置CUDA_VISIBLE_DEVICES，让系统自动分配所有可用GPU")
    
    # 执行训练
    try:
        subprocess.run(base_args, check=True)
        print("✅ 训练完成!")
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return False
    except KeyboardInterrupt:
        print("⏹️ 训练被用户中断")
        return False
    
    return True

def run_evaluation_multi_gpu():
    """运行多GPU评估"""
    
    eval_args = [
        "python", "evaulate.py",
        "--use_multi_gpu", "True",  # 启用多GPU模式
        "--use_fp16", "True",       # 启用FP16
        "--dataset", "wos",
        "--model", "hierVerb",
        "--model_name_or_path", "../chinese-macbert-base",
        "--shot", "16",
        "--seed", "171",
        "--batch_size", "8",
        "--max_seq_lens", "512",
        "--eval_full", "1"
    ]
    
    print("\n🔍 开始多GPU评估...")
    
    try:
        subprocess.run(eval_args, check=True)
        print("✅ 评估完成!")
    except subprocess.CalledProcessError as e:
        print(f"❌ 评估失败: {e}")
        return False
    except KeyboardInterrupt:
        print("⏹️ 评估被用户中断")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🚀 多GPU训练和评估脚本")
    print("=" * 60)
    
    # 检查CUDA可用性
    try:
        import torch
        if not torch.cuda.is_available():
            print("❌ CUDA不可用，无法使用GPU训练")
            sys.exit(1)
        
        gpu_count = torch.cuda.device_count()
        print(f"🔧 检测到 {gpu_count} 个GPU")
        
        if gpu_count < 2:
            print("⚠️  警告: 检测到少于2个GPU，多GPU模式可能无效")
            print("💡 建议: 如果只有1个GPU，请使用单GPU模式")
        
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU状态")
        sys.exit(1)
    
    # 运行训练
    if run_training_multi_gpu():
        print("\n" + "=" * 60)
        # 训练成功后运行评估
        run_evaluation_multi_gpu()
    
    print("\n" + "=" * 60)
    print("🎉 脚本执行完成!")
