# 多GPU训练配置说明

## 概述

本项目现已支持多GPU训练模式，使用 `device_map="auto"` 自动分配GPU资源，无需手动设置 `CUDA_VISIBLE_DEVICES`。

## 主要特性

- ✅ 自动GPU分配：使用 `device_map="auto"` 让系统自动分配所有可用GPU
- ✅ 不设置CUDA_VISIBLE_DEVICES：让所有GPU对程序可见
- ✅ FP16支持：可选择使用半精度浮点数以节省显存
- ✅ 兼容性：保持与原有单GPU模式的完全兼容

## 使用方法

### 1. 多GPU训练

#### 方法一：使用便捷脚本
```bash
cd DCL
python train_multi_gpu.py
```

#### 方法二：手动指定参数
```bash
cd DCL
python models/train.py \
    --use_multi_gpu True \
    --use_fp16 True \
    --dataset wos \
    --model hierVerb \
    --model_name_or_path ../chinese-macbert-base \
    --shot 16 \
    --seed 171 \
    --batch_size 8 \
    --lr 5e-5 \
    --lr2 1e-4 \
    --max_epochs 20
```

### 2. 多GPU评估

```bash
cd DCL
python evaulate.py \
    --use_multi_gpu True \
    --use_fp16 True \
    --dataset wos \
    --model hierVerb \
    --model_name_or_path ../chinese-macbert-base \
    --shot 16 \
    --seed 171 \
    --batch_size 8
```

### 3. 多GPU预测

```python
from predict import HierarchicalClassifier

# 创建分类器实例（多GPU模式）
args = get_default_args()
args.use_multi_gpu = True
args.use_fp16 = True

classifier = HierarchicalClassifier(
    model_ckpt_path="path/to/model.ckpt",
    embedding_pkl_path="path/to/embeddings.pkl",
    args=args
)

# 进行预测
result = classifier.predict("输入文本")
```

## 新增参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--use_multi_gpu` | bool | False | 是否启用多GPU模式 |
| `--use_fp16` | bool | False | 是否使用FP16半精度（节省显存） |

## 工作原理

### 单GPU模式（原有方式）
```python
# 设置可见GPU
os.environ["CUDA_VISIBLE_DEVICES"] = f"{args.device}"
device = torch.device("cuda:0")

# 手动移动模型到GPU
model = model.cuda()
```

### 多GPU模式（新增方式）
```python
# 不设置CUDA_VISIBLE_DEVICES，所有GPU可见
# 使用device_map="auto"自动分配

model = BertForMaskedLM.from_pretrained(
    model_path,
    device_map="auto",  # 自动分配GPU
    torch_dtype=torch.float16  # 可选：使用FP16
)
```

## 性能优化建议

1. **批次大小调整**：多GPU模式下可以使用更大的batch_size（如8-16）
2. **FP16使用**：启用 `--use_fp16 True` 可以节省约50%显存
3. **梯度累积**：如果显存不足，可以增加 `--gradient_accumulation_steps`

## 兼容性说明

- ✅ 完全向后兼容：原有的单GPU训练脚本无需修改
- ✅ 自动检测：系统会自动检测GPU数量并给出建议
- ✅ 错误处理：如果多GPU不可用，会自动降级到单GPU模式

## 故障排除

### 1. 显存不足
```bash
# 启用FP16
--use_fp16 True

# 减小批次大小
--batch_size 4

# 增加梯度累积
--gradient_accumulation_steps 2
```

### 2. GPU不平衡
系统会自动通过 `device_map="auto"` 平衡GPU负载，无需手动干预。

### 3. 单GPU环境
如果只有一个GPU，建议使用单GPU模式：
```bash
python models/train.py --device 0  # 使用GPU 0
```

## 监控GPU使用

训练过程中可以使用以下命令监控GPU使用情况：
```bash
# 实时监控
watch -n 1 nvidia-smi

# 或者
gpustat -i 1
```

## 注意事项

1. **不要设置CUDA_VISIBLE_DEVICES**：多GPU模式下会自动管理GPU可见性
2. **模型保存**：多GPU训练的模型可以在单GPU环境下正常加载
3. **内存需求**：多GPU模式可能需要更多系统内存来管理多个GPU上的数据
