#!/bin/bash

# 多GPU训练快速启动脚本

echo "🚀 多GPU训练快速启动脚本"
echo "================================"

# 检查GPU状态
echo "🔍 检查GPU状态..."
if command -v nvidia-smi &> /dev/null; then
    nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits
else
    echo "⚠️  nvidia-smi 不可用"
fi

echo ""
echo "🧪 运行配置测试..."
python test_multi_gpu.py

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 配置测试通过，开始训练..."
    echo ""
    
    # 运行多GPU训练
    python train_multi_gpu.py
else
    echo ""
    echo "❌ 配置测试失败，请检查环境配置"
    exit 1
fi
