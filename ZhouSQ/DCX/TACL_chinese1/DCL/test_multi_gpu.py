#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多GPU配置测试脚本
用于验证多GPU设置是否正确工作
"""

import os
import sys
import torch
import subprocess
from transformers import BertForMaskedLM, BertTokenizer, BertConfig

def test_gpu_availability():
    """测试GPU可用性"""
    print("🔍 检查GPU可用性...")
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用")
        return False
    
    gpu_count = torch.cuda.device_count()
    print(f"✅ 检测到 {gpu_count} 个GPU")
    
    for i in range(gpu_count):
        gpu_name = torch.cuda.get_device_name(i)
        gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
        print(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    
    return gpu_count > 0

def test_nvidia_smi():
    """测试nvidia-smi命令"""
    print("\n🔍 检查nvidia-smi...")
    
    try:
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ nvidia-smi可用")
            # 显示GPU信息
            lines = result.stdout.split('\n')
            for line in lines:
                if 'GeForce' in line or 'Tesla' in line or 'Quadro' in line or 'RTX' in line:
                    print(f"  {line.strip()}")
            return True
        else:
            print("❌ nvidia-smi不可用")
            return False
    except FileNotFoundError:
        print("❌ nvidia-smi命令未找到")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n🔍 测试模型加载...")
    
    # 测试模型路径
    model_paths = [
        "../chinese-macbert-base",
        "../bert-model",
        "bert-base-chinese"
    ]
    
    model_path = None
    for path in model_paths:
        if os.path.exists(path):
            model_path = path
            break
    
    if model_path is None:
        print("⚠️  未找到本地模型，尝试从HuggingFace下载...")
        model_path = "bert-base-chinese"
    
    try:
        print(f"📦 加载模型: {model_path}")
        
        # 测试单GPU加载
        print("  测试单GPU模式...")
        config = BertConfig.from_pretrained(model_path)
        model_single = BertForMaskedLM.from_pretrained(model_path, config=config)
        tokenizer = BertTokenizer.from_pretrained(model_path)
        print("  ✅ 单GPU模式加载成功")
        
        # 测试多GPU加载
        if torch.cuda.device_count() > 1:
            print("  测试多GPU模式...")
            try:
                model_multi = BertForMaskedLM.from_pretrained(
                    model_path, 
                    config=config,
                    device_map="auto",
                    torch_dtype=torch.float16
                )
                print("  ✅ 多GPU模式加载成功")
                
                # 检查模型分布
                if hasattr(model_multi, 'hf_device_map'):
                    print("  📊 模型分布:")
                    for layer, device in model_multi.hf_device_map.items():
                        print(f"    {layer}: {device}")
                
                return True
            except Exception as e:
                print(f"  ❌ 多GPU模式加载失败: {e}")
                return False
        else:
            print("  ⚠️  只有一个GPU，跳过多GPU测试")
            return True
            
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_data_processing():
    """测试数据处理"""
    print("\n🔍 测试数据处理...")
    
    try:
        # 创建测试数据
        test_text = "这是一个测试文本"
        
        # 简单的tokenizer测试
        from transformers import BertTokenizer
        tokenizer = BertTokenizer.from_pretrained("bert-base-chinese")
        
        inputs = tokenizer(test_text, return_tensors="pt", padding=True, truncation=True)
        print(f"  输入文本: {test_text}")
        print(f"  Token数量: {inputs['input_ids'].shape[1]}")
        print("  ✅ 数据处理正常")
        
        return True
    except Exception as e:
        print(f"❌ 数据处理失败: {e}")
        return False

def test_training_args():
    """测试训练参数"""
    print("\n🔍 测试训练参数...")
    
    try:
        # 模拟参数解析
        class Args:
            def __init__(self):
                self.use_multi_gpu = True
                self.use_fp16 = True
                self.device = 0
                self.dropout = 0.1
        
        args = Args()
        
        print(f"  use_multi_gpu: {args.use_multi_gpu}")
        print(f"  use_fp16: {args.use_fp16}")
        print(f"  device: {args.device}")
        print("  ✅ 参数设置正常")
        
        return True
    except Exception as e:
        print(f"❌ 参数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 多GPU配置测试")
    print("=" * 60)
    
    tests = [
        ("GPU可用性", test_gpu_availability),
        ("nvidia-smi", test_nvidia_smi),
        ("模型加载", test_model_loading),
        ("数据处理", test_data_processing),
        ("训练参数", test_training_args),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！多GPU配置正常")
        print("\n💡 建议:")
        print("  1. 可以开始使用多GPU训练")
        print("  2. 建议启用FP16以节省显存")
        print("  3. 可以适当增加batch_size")
    else:
        print("⚠️  部分测试失败，请检查配置")
        print("\n🔧 故障排除建议:")
        print("  1. 检查CUDA和PyTorch安装")
        print("  2. 确认GPU驱动正常")
        print("  3. 检查模型文件路径")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
